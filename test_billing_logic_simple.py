#!/usr/bin/env python3
"""
Simple test script to verify the billing calculation logic without GUI dependencies.
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def calculate_month_bill_amount(cursor, customer_id, current_bill_amount, bill_type='system'):
    """
    Simplified version of the billing calculation logic for testing
    """
    try:
        # 1. Check if this is the first bill for the customer
        cursor.execute('''
            SELECT COUNT(*) FROM billing WHERE customer_id = ?
        ''', (customer_id,))
        bill_count = cursor.fetchone()[0]
        
        if bill_count == 0:
            # First bill - no previous outstanding or credit
            return current_bill_amount

        # 2. Find the last bill (most recent by year, month, id)
        cursor.execute('''
            SELECT id, status, outstanding_amount, credit_amount, is_manual, amount
            FROM billing
            WHERE customer_id = ?
            ORDER BY year DESC, month DESC, id DESC
            LIMIT 1
        ''', (customer_id,))
        last_bill = cursor.fetchone()
        
        if not last_bill:
            return current_bill_amount

        last_id, last_status, last_outstanding, last_credit, last_is_manual, last_amount = last_bill
        last_outstanding = last_outstanding or 0.0
        last_credit = last_credit or 0.0
        
        # 3. Handle case when last bill is UNPAID
        if last_status == 'Unpaid':
            # Check if there are multiple unpaid bills
            cursor.execute('''
                SELECT COUNT(*), SUM(amount) FROM billing 
                WHERE customer_id = ? AND status = 'Unpaid'
            ''', (customer_id,))
            unpaid_count, unpaid_sum = cursor.fetchone()
            unpaid_sum = unpaid_sum or 0.0
            
            if unpaid_count > 1:
                # Multiple unpaid bills - get last paid bill's outstanding/credit
                cursor.execute('''
                    SELECT outstanding_amount, credit_amount FROM billing
                    WHERE customer_id = ? AND status IN ('Paid', 'Partially Paid')
                    ORDER BY year DESC, month DESC, id DESC 
                    LIMIT 1
                ''', (customer_id,))
                paid_row = cursor.fetchone()
                
                if paid_row:
                    paid_outstanding = paid_row[0] or 0.0
                    paid_credit = paid_row[1] or 0.0
                    
                    if paid_outstanding > 0:
                        return unpaid_sum + paid_outstanding
                    elif paid_credit > 0:
                        return max(unpaid_sum - paid_credit, 0)
                    else:
                        return unpaid_sum
                else:
                    return unpaid_sum
            else:
                # Single unpaid bill (first bill or imported)
                cursor.execute('''
                    SELECT outstanding_amount, credit_balance FROM customers
                    WHERE id = ?
                ''', (customer_id,))
                customer_row = cursor.fetchone()
                
                if customer_row:
                    customer_outstanding = customer_row[0] or 0.0
                    customer_credit = customer_row[1] or 0.0
                    
                    if customer_outstanding > 0:
                        return current_bill_amount + customer_outstanding
                    elif customer_credit > 0:
                        return max(current_bill_amount - customer_credit, 0)
                    else:
                        return current_bill_amount
                else:
                    return current_bill_amount
        
        # 4. Handle case when last bill is PAID or PARTIALLY PAID
        elif last_status in ('Paid', 'Partially Paid'):
            if last_outstanding > 0:
                return current_bill_amount + last_outstanding
            elif last_credit > 0:
                return max(current_bill_amount - last_credit, 0)
            else:
                return current_bill_amount
        
        return current_bill_amount

    except Exception as e:
        print(f"Error calculating month bill amount: {str(e)}")
        return current_bill_amount

def test_billing_scenarios():
    """Test the billing scenarios"""
    print("Testing billing calculation logic...")
    
    # Connect to database
    conn = sqlite3.connect('crm_system.db')
    c = conn.cursor()
    
    try:
        # Test Scenario 1: Paid bill with outstanding
        print("\n=== Test 1: Paid Bill with Outstanding ===")
        
        # Create test customer
        c.execute("DELETE FROM customers WHERE name = 'Test Customer 1'")
        c.execute("DELETE FROM billing WHERE customer_id IN (SELECT id FROM customers WHERE name = 'Test Customer 1')")
        
        c.execute('''
            INSERT INTO customers (name, username, phone, package_id, region, status, 
                                 credit_balance, outstanding_amount)
            VALUES ('Test Customer 1', 'test1', '1234567890', 1, 'Test', 1, 0, 0)
        ''')
        customer_id = c.lastrowid
        
        # Create a paid bill with outstanding
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual, paid_date, paid_by)
            VALUES (?, 6, 2024, 1000, 1200, 1000, 200, 0, 'Paid', 'INV-TEST1', 0, 
                    datetime('now'), 'Test User')
        ''', (customer_id,))
        
        # Test calculation
        result = calculate_month_bill_amount(c, customer_id, 1000, 'system')
        expected = 1000 + 200  # package price + outstanding
        print(f"System bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        print("✓ Test 1 passed")
        
        # Test Scenario 2: Paid bill with credit
        print("\n=== Test 2: Paid Bill with Credit ===")
        
        # Clear previous data
        c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
        
        # Create a paid bill with credit
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual, paid_date, paid_by)
            VALUES (?, 6, 2024, 1000, 1000, 1300, 0, 300, 'Paid', 'INV-TEST2', 0, 
                    datetime('now'), 'Test User')
        ''', (customer_id,))
        
        # Test calculation
        result = calculate_month_bill_amount(c, customer_id, 1000, 'system')
        expected = max(1000 - 300, 0)  # package price - credit
        print(f"System bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        print("✓ Test 2 passed")
        
        # Test Scenario 3: First bill
        print("\n=== Test 3: First Bill ===")
        
        # Create new customer
        c.execute("DELETE FROM customers WHERE name = 'Test Customer 2'")
        c.execute('''
            INSERT INTO customers (name, username, phone, package_id, region, status, 
                                 credit_balance, outstanding_amount)
            VALUES ('Test Customer 2', 'test2', '1234567891', 1, 'Test', 1, 0, 0)
        ''')
        customer_id2 = c.lastrowid
        
        # Test first bill calculation
        result = calculate_month_bill_amount(c, customer_id2, 1000, 'system')
        expected = 1000  # package price (first bill)
        print(f"First bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        print("✓ Test 3 passed")
        
        print("\n🎉 All billing logic tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()
    
    return True

if __name__ == "__main__":
    success = test_billing_scenarios()
    sys.exit(0 if success else 1)
