#!/usr/bin/env python3
"""
Test script to verify the billing logic fixes work correctly.
Tests all scenarios described in the user requirements.
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import Database
from views.billing import BillingManager

def setup_test_data():
    """Set up test data for billing scenarios"""
    db = Database()
    
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Clear existing test data
        c.execute("DELETE FROM payment_history WHERE customer_id IN (SELECT id FROM customers WHERE name LIKE 'Test%')")
        c.execute("DELETE FROM billing WHERE customer_id IN (SELECT id FROM customers WHERE name LIKE 'Test%')")
        c.execute("DELETE FROM customers WHERE name LIKE 'Test%'")
        c.execute("DELETE FROM packages WHERE name = 'Test Package'")
        c.execute("DELETE FROM regions WHERE name = 'Test Region'")
        
        # Create test region
        c.execute("INSERT INTO regions (name) VALUES ('Test Region')")
        
        # Create test package
        c.execute("INSERT INTO packages (name, price) VALUES ('Test Package', 1000)")
        package_id = c.lastrowid
        
        # Create test customers
        customers = [
            ('Test Customer 1', 'test1', '1234567890', 0, 0),  # No credit, no outstanding
            ('Test Customer 2', 'test2', '1234567891', 500, 0),  # Has credit
            ('Test Customer 3', 'test3', '1234567892', 0, 200),  # Has outstanding
            ('Test Customer 4', 'test4', '1234567893', 0, 0),  # For unpaid bill scenario
        ]
        
        customer_ids = []
        for name, username, phone, credit, outstanding in customers:
            c.execute('''
                INSERT INTO customers (name, username, phone, package_id, region, status, 
                                     credit_balance, outstanding_amount)
                VALUES (?, ?, ?, ?, 'Test Region', 1, ?, ?)
            ''', (name, username, phone, package_id, credit, outstanding))
            customer_ids.append(c.lastrowid)
        
        conn.commit()
        return customer_ids, package_id

def test_scenario_1_paid_bill_with_outstanding():
    """Test: When last bill is paid with outstanding > 0"""
    print("\n=== Test Scenario 1: Paid Bill with Outstanding ===")
    
    customer_ids, package_id = setup_test_data()
    customer_id = customer_ids[2]  # Customer with outstanding
    
    db = Database()
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Create a paid bill with outstanding
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual, paid_date, paid_by)
            VALUES (?, 6, 2024, 1000, 1200, 1000, 200, 0, 'Paid', 'INV-0001', 0, 
                    datetime('now'), 'Test User')
        ''', (customer_id,))
        
        # Test system bill calculation
        billing_manager = BillingManager(None, {})
        result = billing_manager._calculate_month_bill_amount(c, customer_id, None, 1000, 'system')
        
        expected = 1000 + 200  # package price + outstanding
        print(f"System bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        # Test manual bill calculation
        result = billing_manager._calculate_month_bill_amount(c, customer_id, None, 1500, 'manual')
        
        expected = 1500 + 200  # manual amount + outstanding
        print(f"Manual bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        # Test product bill calculation
        result = billing_manager._calculate_month_bill_amount(c, customer_id, None, 800, 'product')
        
        expected = 800 + 200  # product amount + outstanding
        print(f"Product bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        print("✓ Scenario 1 passed")

def test_scenario_2_paid_bill_with_credit():
    """Test: When last bill is paid with credit > 0"""
    print("\n=== Test Scenario 2: Paid Bill with Credit ===")
    
    customer_ids, package_id = setup_test_data()
    customer_id = customer_ids[1]  # Customer with credit
    
    db = Database()
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Create a paid bill with credit
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual, paid_date, paid_by)
            VALUES (?, 6, 2024, 1000, 1000, 1300, 0, 300, 'Paid', 'INV-0002', 0, 
                    datetime('now'), 'Test User')
        ''', (customer_id,))
        
        # Test system bill calculation
        billing_manager = BillingManager(None, {})
        result = billing_manager._calculate_month_bill_amount(c, customer_id, None, 1000, 'system')
        
        expected = max(1000 - 300, 0)  # package price - credit
        print(f"System bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        # Test manual bill calculation
        result = billing_manager._calculate_month_bill_amount(c, customer_id, None, 1500, 'manual')
        
        expected = max(1500 - 300, 0)  # manual amount - credit
        print(f"Manual bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        # Test product bill calculation
        result = billing_manager._calculate_month_bill_amount(c, customer_id, None, 800, 'product')
        
        expected = max(800 - 300, 0)  # product amount - credit
        print(f"Product bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        print("✓ Scenario 2 passed")

def test_scenario_3_unpaid_bill_first():
    """Test: When last bill is unpaid (first bill)"""
    print("\n=== Test Scenario 3: Unpaid Bill (First Bill) ===")
    
    customer_ids, package_id = setup_test_data()
    customer_id = customer_ids[0]  # Customer with no credit/outstanding
    
    db = Database()
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Test first bill calculation
        billing_manager = BillingManager(None, {})
        result = billing_manager._calculate_month_bill_amount(c, customer_id, None, 1000, 'system')
        
        expected = 1000  # package price (first bill)
        print(f"First system bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        print("✓ Scenario 3 passed")

def test_scenario_4_multiple_unpaid_bills():
    """Test: When multiple unpaid bills exist"""
    print("\n=== Test Scenario 4: Multiple Unpaid Bills ===")
    
    customer_ids, package_id = setup_test_data()
    customer_id = customer_ids[3]  # Customer for unpaid bill scenario
    
    db = Database()
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Create a paid bill with outstanding first
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual, paid_date, paid_by)
            VALUES (?, 5, 2024, 1000, 1000, 1000, 150, 0, 'Paid', 'INV-0003', 0, 
                    datetime('now'), 'Test User')
        ''', (customer_id,))
        
        # Create unpaid bills
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual)
            VALUES (?, 6, 2024, 1000, 1000, 0, 1000, 0, 'Unpaid', 'INV-0004', 0)
        ''', (customer_id,))
        
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual)
            VALUES (?, 7, 2024, 1000, 1000, 0, 1000, 0, 'Unpaid', 'INV-0005', 0)
        ''', (customer_id,))
        
        # Test new bill calculation with multiple unpaid bills
        billing_manager = BillingManager(None, {})
        result = billing_manager._calculate_month_bill_amount(c, customer_id, None, 1000, 'system')
        
        expected = 2000 + 150  # sum of unpaid bills + last paid bill outstanding
        print(f"New bill with multiple unpaid: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        print("✓ Scenario 4 passed")

def test_payment_processing():
    """Test payment processing and Total Paid column"""
    print("\n=== Test Payment Processing ===")
    
    customer_ids, package_id = setup_test_data()
    customer_id = customer_ids[0]
    
    db = Database()
    with db.get_connection_context() as conn:
        c = conn.cursor()
        
        # Create an unpaid bill
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual)
            VALUES (?, 7, 2024, 1000, 3000, 0, 3000, 0, 'Unpaid', 'INV-0006', 0)
        ''', (customer_id,))
        
        bill_id = c.lastrowid
        
        # Simulate payment of 3500 PKR
        payment_amount = 3500
        month_bill_amount = 3000
        
        # Calculate payment results
        pay_amount = min(payment_amount, month_bill_amount)
        new_paid = pay_amount
        new_outstanding = max(month_bill_amount - new_paid, 0)
        bill_credit = max(new_paid - month_bill_amount, 0)
        new_status = 'Paid' if new_outstanding == 0 else 'Partially Paid'
        
        # Update billing record
        c.execute('''
            UPDATE billing SET
            paid_amount = ?,
            outstanding_amount = ?,
            credit_amount = ?,
            status = ?,
            paid_date = CURRENT_TIMESTAMP,
            paid_by = ?
            WHERE id = ?
        ''', (new_paid, new_outstanding, bill_credit, new_status, 'Test User', bill_id))
        
        # Record in payment history
        c.execute('''
            INSERT INTO payment_history
            (bill_id, invoice_number, customer_id, payment_date, amount_paid,
             credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
             payment_method, recorded_by, timestamp)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        ''', (bill_id, 'INV-0006', customer_id, pay_amount, bill_credit, new_outstanding, new_outstanding, bill_credit, None, 'Test User'))
        
        # Verify payment history shows correct amount
        c.execute('SELECT amount_paid FROM payment_history WHERE bill_id = ?', (bill_id,))
        recorded_payment = c.fetchone()[0]
        
        print(f"Payment amount: {payment_amount}, Month bill: {month_bill_amount}")
        print(f"Recorded payment: {recorded_payment}, Expected: {pay_amount}")
        assert recorded_payment == pay_amount, f"Expected {pay_amount}, got {recorded_payment}"
        
        # Verify bill status
        c.execute('SELECT status, paid_amount FROM billing WHERE id = ?', (bill_id,))
        status, paid_amount = c.fetchone()
        print(f"Bill status: {status}, Paid amount: {paid_amount}")
        assert status == 'Paid', f"Expected 'Paid', got {status}"
        assert paid_amount == 3000, f"Expected 3000, got {paid_amount}"
        
        print("✓ Payment processing test passed")

def run_all_tests():
    """Run all billing tests"""
    print("Starting billing logic tests...")
    
    try:
        test_scenario_1_paid_bill_with_outstanding()
        test_scenario_2_paid_bill_with_credit()
        test_scenario_3_unpaid_bill_first()
        test_scenario_4_multiple_unpaid_bills()
        test_payment_processing()
        
        print("\n🎉 All tests passed! Billing logic fixes are working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
