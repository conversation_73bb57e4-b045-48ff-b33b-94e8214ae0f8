#!/usr/bin/env python3
"""
Comprehensive test suite for the new billing calculation logic.

This test verifies all the billing scenarios according to the new requirements:

1. When last bill is "paid" or "partially paid":
   - Case 1 (System): outstanding > 0 → bill = package price + outstanding
                      credit > 0 → bill = package price - credit
   - Case 2 (Manual): outstanding > 0 → bill = manual amount + outstanding
                      credit > 0 → bill = manual amount - credit  
   - Case 3 (Product): outstanding > 0 → bill = product amount + outstanding
                       credit > 0 → bill = product amount - credit

2. When last bill is "unpaid":
   - First bill (new customer): credit = 0, outstanding = 0, bill = package price
   - First bill (imported): can have credit > 0 OR outstanding > 0 (not both)
   - Multiple unpaid bills: sum all unpaid + last paid bill's outstanding/credit
"""

import sys
import os
import sqlite3
import tempfile
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from views.billing import BillingManager
import tkinter as tk

class TestBillingLogic:
    def __init__(self):
        self.test_db_path = None
        self.setup_test_database()
        
    def setup_test_database(self):
        """Create a temporary test database with sample data"""
        # Create temporary database
        fd, self.test_db_path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        
        conn = sqlite3.connect(self.test_db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE customers (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                package_id INTEGER,
                outstanding_amount REAL DEFAULT 0,
                credit_balance REAL DEFAULT 0,
                status INTEGER DEFAULT 1
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE packages (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                price REAL NOT NULL
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE billing (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                month INTEGER,
                year INTEGER,
                amount REAL,
                month_bill_amount REAL,
                paid_amount REAL DEFAULT 0,
                outstanding_amount REAL DEFAULT 0,
                credit_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'Unpaid',
                invoice_number TEXT,
                is_manual INTEGER DEFAULT 0,
                paid_date TEXT,
                paid_by TEXT,
                created_date TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE customer_purchases (
                id INTEGER PRIMARY KEY,
                customer_id INTEGER,
                product_id INTEGER,
                billing_id INTEGER,
                purchase_date TEXT,
                price REAL
            )
        ''')
        
        # Insert test data
        # Package
        cursor.execute('INSERT INTO packages (id, name, price) VALUES (1, "Basic Package", 1000.0)')
        
        # Test customers
        cursor.execute('INSERT INTO customers (id, name, package_id, outstanding_amount, credit_balance) VALUES (1, "Test Customer 1", 1, 0, 0)')
        cursor.execute('INSERT INTO customers (id, name, package_id, outstanding_amount, credit_balance) VALUES (2, "Test Customer 2", 1, 500, 0)')
        cursor.execute('INSERT INTO customers (id, name, package_id, outstanding_amount, credit_balance) VALUES (3, "Test Customer 3", 1, 0, 300)')
        cursor.execute('INSERT INTO customers (id, name, package_id, outstanding_amount, credit_balance) VALUES (4, "Test Customer 4", 1, 0, 0)')
        
        conn.commit()
        conn.close()
        
    def get_test_connection(self):
        """Get connection to test database"""
        return sqlite3.connect(self.test_db_path)
        
    def test_scenario_1_paid_bill_with_outstanding(self):
        """Test: Last bill paid with outstanding > 0"""
        print("\n=== Test Scenario 1: Last bill paid with outstanding ===")
        
        conn = self.get_test_connection()
        cursor = conn.cursor()
        
        # Create a paid bill with outstanding
        cursor.execute('''
            INSERT INTO billing (customer_id, month, year, amount, status, outstanding_amount, credit_amount)
            VALUES (1, 1, 2024, 1000, 'Paid', 200, 0)
        ''')
        conn.commit()
        
        # Test system bill calculation
        billing_manager = BillingManager(None, {})
        result = billing_manager._calculate_month_bill_amount(
            cursor, customer_id=1, current_bill_id=None, current_bill_amount=1000,
            bill_type='system', is_first_bill=False
        )
        
        expected = 1000 + 200  # package price + outstanding
        print(f"System bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        # Test manual bill calculation
        result = billing_manager._calculate_month_bill_amount(
            cursor, customer_id=1, current_bill_id=None, current_bill_amount=1500,
            bill_type='manual', is_first_bill=False
        )
        
        expected = 1500 + 200  # manual amount + outstanding
        print(f"Manual bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        # Test product bill calculation
        result = billing_manager._calculate_month_bill_amount(
            cursor, customer_id=1, current_bill_id=None, current_bill_amount=800,
            bill_type='product', is_first_bill=False
        )
        
        expected = 800 + 200  # product amount + outstanding
        print(f"Product bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        conn.close()
        print("✓ Scenario 1 passed")
        
    def test_scenario_2_paid_bill_with_credit(self):
        """Test: Last bill paid with credit > 0"""
        print("\n=== Test Scenario 2: Last bill paid with credit ===")
        
        conn = self.get_test_connection()
        cursor = conn.cursor()
        
        # Create a paid bill with credit
        cursor.execute('''
            INSERT INTO billing (customer_id, month, year, amount, status, outstanding_amount, credit_amount)
            VALUES (2, 1, 2024, 1000, 'Paid', 0, 300)
        ''')
        conn.commit()
        
        billing_manager = BillingManager(None, {})
        
        # Test system bill calculation
        result = billing_manager._calculate_month_bill_amount(
            cursor, customer_id=2, current_bill_id=None, current_bill_amount=1000,
            bill_type='system', is_first_bill=False
        )
        
        expected = max(1000 - 300, 0)  # package price - credit
        print(f"System bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        # Test manual bill calculation
        result = billing_manager._calculate_month_bill_amount(
            cursor, customer_id=2, current_bill_id=None, current_bill_amount=1500,
            bill_type='manual', is_first_bill=False
        )
        
        expected = max(1500 - 300, 0)  # manual amount - credit
        print(f"Manual bill: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        conn.close()
        print("✓ Scenario 2 passed")
        
    def test_scenario_3_first_bill_new_customer(self):
        """Test: First bill for new customer"""
        print("\n=== Test Scenario 3: First bill new customer ===")
        
        conn = self.get_test_connection()
        cursor = conn.cursor()
        
        billing_manager = BillingManager(None, {})
        
        # Test system bill calculation
        result = billing_manager._calculate_month_bill_amount(
            cursor, customer_id=1, current_bill_id=None, current_bill_amount=1000,
            bill_type='system', is_first_bill=True, imported=False
        )
        
        expected = 1000  # package price only
        print(f"System bill (new customer): Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        conn.close()
        print("✓ Scenario 3 passed")
        
    def test_scenario_4_first_bill_imported_customer(self):
        """Test: First bill for imported customer with credit/outstanding"""
        print("\n=== Test Scenario 4: First bill imported customer ===")
        
        conn = self.get_test_connection()
        cursor = conn.cursor()
        
        billing_manager = BillingManager(None, {})
        
        # Test imported customer with outstanding
        result = billing_manager._calculate_month_bill_amount(
            cursor, customer_id=2, current_bill_id=None, current_bill_amount=1000,
            bill_type='system', is_first_bill=True, imported=True
        )
        
        expected = 1000 + 500  # package price + outstanding
        print(f"Imported with outstanding: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        # Test imported customer with credit
        result = billing_manager._calculate_month_bill_amount(
            cursor, customer_id=3, current_bill_id=None, current_bill_amount=1000,
            bill_type='system', is_first_bill=True, imported=True
        )
        
        expected = max(1000 - 300, 0)  # package price - credit
        print(f"Imported with credit: Expected {expected}, Got {result}")
        assert result == expected, f"Expected {expected}, got {result}"
        
        conn.close()
        print("✓ Scenario 4 passed")
        
    def run_all_tests(self):
        """Run all test scenarios"""
        print("Starting comprehensive billing logic tests...")
        
        try:
            self.test_scenario_1_paid_bill_with_outstanding()
            self.test_scenario_2_paid_bill_with_credit()
            self.test_scenario_3_first_bill_new_customer()
            self.test_scenario_4_first_bill_imported_customer()
            
            print("\n🎉 All tests passed! New billing logic is working correctly.")
            
        except Exception as e:
            print(f"\n❌ Test failed: {str(e)}")
            raise
        finally:
            # Cleanup
            if self.test_db_path and os.path.exists(self.test_db_path):
                os.unlink(self.test_db_path)

if __name__ == "__main__":
    # Create a minimal tkinter root for the BillingManager
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    test_suite = TestBillingLogic()
    test_suite.run_all_tests()
    
    root.destroy()
