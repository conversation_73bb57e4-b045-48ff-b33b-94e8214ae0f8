import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import re
from datetime import datetime
from PIL import Image, ImageTk
import pandas as pd
import logging
try:
    from login import current_user
except ImportError:
    current_user = None
from database_utils import get_db_connection, execute_with_retry
from resources import resource_path
from database import Database

class Tooltip:
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip = None
        self.widget.bind("<Enter>", self.enter)
        self.widget.bind("<Leave>", self.leave)
        self.widget.bind("<ButtonPress>", self.leave)

    def enter(self, event=None):
        self.tooltip = tk.Toplevel(self.widget)
        self.tooltip.wm_overrideredirect(True)
        self.tooltip.wm_attributes("-topmost", True)
        
        label = tk.Label(
            self.tooltip,
            text=self.text,
            background="#4A6FA5",
            relief="solid",
            borderwidth=1,
            font=("Helvetica", 10),
            fg="#FFFFFF"
        )
        label.pack()
        
        widget_x = self.widget.winfo_rootx()
        widget_y = self.widget.winfo_rooty()
        widget_width = self.widget.winfo_width()
        widget_height = self.widget.winfo_height()
        
        self.tooltip.update_idletasks()
        tooltip_width = self.tooltip.winfo_width()
        tooltip_height = self.tooltip.winfo_height()
        
        screen_width = self.widget.winfo_screenwidth()
        screen_height = self.widget.winfo_screenheight()
        
        x = widget_x + (widget_width - tooltip_width) // 2
        y = widget_y + widget_height + 5
        
        if x + tooltip_width > screen_width:
            x = screen_width - tooltip_width - 5
        elif x < 0:
            x = 5
            
        if y + tooltip_height > screen_height:
            y = widget_y - tooltip_height - 5
            if y < 0:
                y = 5
        
        self.tooltip.wm_geometry(f"+{int(x)}+{int(y)}")

    def leave(self, event=None):
        if self.tooltip:
            self.tooltip.destroy()
            self.tooltip = None

class CustomerManager(tk.Frame):
    COLORS = {
        'background': '#F8F9FA',
        'card_bg': '#FFFFFF',
        'primary_accent': '#4A6FA5',
        'secondary_accent': '#6C8FC7',
        'text_primary': '#2D3748',
        'text_secondary': '#718096',
        'button_start': '#4A6FA5',
        'button_end': '#3A5A8C',
        'transparent': 'transparent',
        'warning': '#E53E3E',
        'input_bg': '#EDF2F7',
        'border': '#E2E8F0',
        'suspended_row': '#FDF6BE'
    }

    def __init__(self, parent, nav_commands, customer_id=None):
        super().__init__(parent)
        self.nav = nav_commands
        self.current_year = datetime.now().year
        self.current_month = datetime.now().month
        self.db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        self._dialogs = []  # Track open dialogs
        self.db = Database()  # Add database instance
        self.customer_id = customer_id  # Store the customer_id if provided
        self._update_customer_table_schema()

        self.config(bg=self.COLORS['background'])
        self.pack(fill="both", expand=True)

        # Header
        header = tk.Frame(self, bg=self.COLORS['primary_accent'], height=80)
        header.pack(fill="x", pady=(0, 20))

        # Dashboard Button with Icon
        self.icons = {}
        icon_paths = {
            'dashboard': 'assets/dashboard/customers/dashboard.png',
            'bill_details': 'assets/dashboard/customers/billdetails.png',
            'edit': 'assets/dashboard/customers/edituser.png',
            'add_customer': 'assets/dashboard/customers/adduser.png',
            'activate': 'assets/dashboard/customers/activateuser.png',
            'suspend': 'assets/dashboard/customers/suspenduser.png',
            'change_plan': 'assets/dashboard/customers/changepackageplan.png',
            'buy_product': 'assets/dashboard/products/buyproduct.png',
            'delete_user': 'assets/dashboard/customers/deleteuser.png',
            'billing_nav': 'assets/dashboard/billing/left-arrow.png'
        }

        for icon_name, relative_path in icon_paths.items():
            try:
                full_path = resource_path(relative_path)
                if os.path.exists(full_path):
                    img = Image.open(full_path).convert("RGBA")
                    data = img.getdata()
                    new_data = []
                    for item in data:
                        if item[3] > 0:  # If pixel is not transparent
                            new_data.append((255, 255, 255, item[3]))  # Set to white
                        else:
                            new_data.append(item)
                    img.putdata(new_data)
                    img = img.resize((30, 30), Image.Resampling.LANCZOS)
                    self.icons[icon_name] = ImageTk.PhotoImage(img)
                else:
                    print(f"Icon not found: {full_path}")
                    self.icons[icon_name] = ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))
            except Exception as e:
                print(f"Error loading icon {icon_name}: {str(e)}")
                self.icons[icon_name] = ImageTk.PhotoImage(Image.new('RGBA', (30, 30), (255, 255, 255, 0)))

        dashboard_btn = tk.Button(header, image=self.icons['dashboard'], command=self.nav['show_dashboard'],
                                 bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                 activebackground=self.COLORS['secondary_accent'])
        dashboard_btn.pack(side="left", padx=10)
        Tooltip(dashboard_btn, "Dashboard")

        tk.Label(header, text="Customer Management", font=("Helvetica", 24, "bold"),
                 fg="#FFFFFF", bg=self.COLORS['primary_accent']).place(relx=0.5, rely=0.5, anchor="center")

        # Action Buttons in Header (Right-aligned)
        btn_frame = tk.Frame(header, bg=self.COLORS['primary_accent'])
        btn_frame.pack(side="right", padx=10)

        billing_nav_btn = tk.Button(btn_frame, image=self.icons['billing_nav'], command=self.nav['show_billing'],
                                   bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                   activebackground=self.COLORS['secondary_accent'])
        billing_nav_btn.pack(side="left", padx=5)
        Tooltip(billing_nav_btn, "Billing")

        self.bill_btn = tk.Button(btn_frame, image=self.icons['bill_details'], command=self._show_billing,
                                 bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                 activebackground=self.COLORS['secondary_accent'])
        self.bill_btn.pack(side="left", padx=5)
        Tooltip(self.bill_btn, "Bill Details")

        self.edit_btn = tk.Button(btn_frame, image=self.icons['edit'], command=self._edit_customer,
                                 bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                 activebackground=self.COLORS['secondary_accent'])
        self.edit_btn.pack(side="left", padx=5)
        Tooltip(self.edit_btn, "Edit")

        add_customer_btn = tk.Button(btn_frame, image=self.icons['add_customer'], command=self._add_customer,
                                    bg=self.COLORS['primary_accent'], relief=tk.FLAT,
                                    activebackground=self.COLORS['secondary_accent'])
        add_customer_btn.pack(side="left", padx=5)
        Tooltip(add_customer_btn, "Add Customer")

        self.active_btn = tk.Button(btn_frame, image=self.icons['activate'], command=self._activate_customer,
                                   bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                   activebackground=self.COLORS['secondary_accent'])
        self.active_btn.pack(side="left", padx=5)
        Tooltip(self.active_btn, "Activate")

        self.suspend_btn = tk.Button(btn_frame, image=self.icons['suspend'], command=self._suspend_customer,
                                    bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                    activebackground=self.COLORS['secondary_accent'])
        self.suspend_btn.pack(side="left", padx=5)
        Tooltip(self.suspend_btn, "Suspend")

        self.change_plan_btn = tk.Button(btn_frame, image=self.icons['change_plan'], command=self._change_package_plan,
                                       bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                       activebackground=self.COLORS['secondary_accent'])
        self.change_plan_btn.pack(side="left", padx=5)
        Tooltip(self.change_plan_btn, "Change Package Plan")

        self.buy_product_btn = tk.Button(btn_frame, image=self.icons['buy_product'], command=self._buy_product,
                                        bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                        activebackground=self.COLORS['secondary_accent'])
        self.buy_product_btn.pack(side="left", padx=5)
        Tooltip(self.buy_product_btn, "Buy Product")

        self.delete_btn = tk.Button(btn_frame, image=self.icons['delete_user'], command=self._delete_customer,
                                    bg=self.COLORS['primary_accent'], relief=tk.FLAT, state="disabled",
                                    activebackground=self.COLORS['secondary_accent'])
        self.delete_btn.pack(side="left", padx=5)
        Tooltip(self.delete_btn, "Delete Customer")

        # Main Content
        content = tk.Frame(self, bg=self.COLORS['card_bg'])
        content.pack(fill="both", expand=True, padx=5, pady=5)

        # Filter and Search Controls
        filter_frame = tk.Frame(content, bg=self.COLORS['card_bg'])
        filter_frame.pack(fill="x", pady=(0, 5))

        tk.Label(filter_frame, text="Filter by Region:", bg=self.COLORS['card_bg'],
                 fg=self.COLORS['text_primary'], font=("Helvetica", 10)).pack(side="left", padx=5)

        self.region_filter_var = tk.StringVar(value="All")
        self.region_filter_dropdown = ttk.Combobox(filter_frame, textvariable=self.region_filter_var,
                                                  state="readonly", font=("Helvetica", 12), width=20)
        self.region_filter_dropdown.pack(side="left", padx=5)
        self.region_filter_dropdown.bind("<<ComboboxSelected>>", lambda e: self._load_customers())

        tk.Label(filter_frame, text="Search Customer:", font=("Helvetica", 10),
                 bg=self.COLORS['card_bg'], fg=self.COLORS['text_primary']).pack(side="left", padx=(10, 5))

        self.customer_search_var = tk.StringVar()
        self.customer_search_var.trace('w', self._on_search_change)
        
        self.customer_combobox = ttk.Combobox(filter_frame, textvariable=self.customer_search_var,
                                             font=("Helvetica", 12), width=20)
        self.customer_combobox.pack(side="left", padx=5)
        self.customer_combobox.bind("<<ComboboxSelected>>", lambda e: self._on_customer_selected())

        # Customer Table
        tree_frame = tk.Frame(content, bg=self.COLORS['card_bg'])
        tree_frame.pack(fill="both", expand=True, pady=(0, 5))

        # Configure Treeview style
        style = ttk.Style()
        style.configure("Treeview",
                        background=self.COLORS['card_bg'],
                        foreground=self.COLORS['text_secondary'],
                        fieldbackground=self.COLORS['card_bg'],
                        font=("Helvetica", 10),
                        rowheight=25,
                        borderwidth=1,
                        relief="solid",
                        bordercolor=self.COLORS['border'])

        style.configure("Treeview.Heading",
                        font=("Helvetica", 10, "bold"),
                        foreground=self.COLORS['text_secondary'],
                        background=self.COLORS['input_bg'],
                        relief="flat",
                        padding=5)

        style.configure("Suspended.Treeview",
                        background=self.COLORS['suspended_row'],
                        foreground=self.COLORS['text_secondary'])

        style.map("Treeview", 
                  background=[('selected', self.COLORS['primary_accent'])],
                  foreground=[('selected', '#FFFFFF')])

        scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL)
        scrollbar.pack(side="right", fill="y")

        columns = ("id", "user_name", "name", "phone", "package", "region", "status")
        self.tree = ttk.Treeview(tree_frame, 
                                columns=columns,
                                selectmode="browse",
                                show="headings",
                                yscrollcommand=scrollbar.set,
                                style="Treeview")
        for col in columns:
            if col in ("user_name", "name"):
                self.tree.heading(col, text=col.replace("_", " ").title(), anchor="w")
            else:
                self.tree.heading(col, text=col.replace("_", " ").title(), anchor="center")

        self.tree.column("id", width=50, anchor="center")
        self.tree.column("user_name", width=120, anchor="w")
        self.tree.column("name", width=150, anchor="w")
        self.tree.column("phone", width=120, anchor="center")
        self.tree.column("package", width=80, anchor="center")
        self.tree.column("region", width=80, anchor="center")
        self.tree.column("status", width=80, anchor="center")

        scrollbar.config(command=self.tree.yview)
        self.tree.pack(fill="both", expand=True)

        self.tree.bind("<<TreeviewSelect>>", self._on_row_select)
        self._load_regions()
        self._load_customers()
        self._load_packages()
        
        # Bind minimize/restore events to main window
        self.winfo_toplevel().bind('<Unmap>', self._on_main_minimize)
        self.winfo_toplevel().bind('<Map>', self._on_main_restore)
        # If customer_id is provided, select and focus that customer
        if self.customer_id is not None:
            self.after(100, self._select_customer_by_id)

    def _update_customer_table_schema(self):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("PRAGMA table_info(customers)")
                columns = {col[1]: col for col in c.fetchall()}

                if 'status' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN status INTEGER DEFAULT 1")
                elif columns['status'][2] != 'INTEGER':
                    c.execute('''CREATE TABLE customers_new (
                                 id INTEGER PRIMARY KEY AUTOINCREMENT,
                                 user_name TEXT NOT NULL UNIQUE,
                                 name TEXT NOT NULL,
                                 phone TEXT,
                                 package_id INTEGER,
                                 create_date TEXT,
                                 status INTEGER DEFAULT 1,
                                 package_change_date TEXT,
                                 region TEXT,
                                 FOREIGN KEY (package_id) REFERENCES packages(id)
                                 )''')
                    c.execute('''INSERT INTO customers_new (id, user_name, name, phone, package_id, create_date, status, region)
                                 SELECT id, name, name, phone, package_id, create_date,
                                        CASE WHEN status = 'Active' THEN 1 ELSE 0 END, region
                                 FROM customers''')
                    c.execute("DROP TABLE customers")
                    c.execute("ALTER TABLE customers_new RENAME TO customers")

                if 'package_change_date' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN package_change_date TEXT")

                if 'region' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN region TEXT")

                if 'user_name' not in columns:
                    c.execute("ALTER TABLE customers ADD COLUMN user_name TEXT")
                    c.execute("UPDATE customers SET user_name = name WHERE user_name IS NULL")
                    c.execute("ALTER TABLE customers RENAME TO customers_old")
                    c.execute('''CREATE TABLE customers (
                                 id INTEGER PRIMARY KEY AUTOINCREMENT,
                                 user_name TEXT NOT NULL UNIQUE,
                                 name TEXT NOT NULL,
                                 phone TEXT,
                                 package_id INTEGER,
                                 create_date TEXT,
                                 status INTEGER DEFAULT 1,
                                 package_change_date TEXT,
                                 region TEXT,
                                 FOREIGN KEY (package_id) REFERENCES packages(id)
                                 )''')
                    c.execute('''INSERT INTO customers (id, user_name, name, phone, package_id, create_date, status, package_change_date, region)
                                 SELECT id, user_name, name, phone, package_id, create_date, status, package_change_date, region
                                 FROM customers_old''')
                    c.execute("DROP TABLE customers_old")

                conn.commit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to update customer table schema: {str(e)}")

    def _validate_phone(self, phone):
        if not phone:
            return True
        return re.match(r'^03\d{9}$', phone) is not None

    def _validate_user_name(self, user_name):
        if not user_name:
            raise ValueError("User name is required")
        if len(user_name) > 20:
            raise ValueError("User name must not exceed 20 characters")
        if not re.match(r'^[a-zA-Z0-9.]+$', user_name):
            raise ValueError("User name must contain only letters, numbers, and dots")
        return user_name

    def _on_row_select(self, event):
        selected = self.tree.selection()
        if selected:
            self.bill_btn.config(state="normal")
            self.edit_btn.config(state="normal")
            self.change_plan_btn.config(state="normal")
            self.buy_product_btn.config(state="normal")
            self.delete_btn.config(state="normal")
            customer_status = self.tree.item(selected[0])['values'][6]
            if customer_status == 'Active':
                self.active_btn.config(state="disabled")
                self.suspend_btn.config(state="normal")
            else:
                self.active_btn.config(state="normal")
                self.suspend_btn.config(state="disabled")
        else:
            self.bill_btn.config(state="disabled")
            self.edit_btn.config(state="disabled")
            self.active_btn.config(state="disabled")
            self.suspend_btn.config(state="disabled")
            self.change_plan_btn.config(state="disabled")
            self.buy_product_btn.config(state="disabled")
            self.delete_btn.config(state="disabled")

    def _load_regions(self):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT name FROM regions ORDER BY name")
                self.regions = [row[0] for row in c.fetchall()]
                self.region_filter_dropdown['values'] = ['All'] + self.regions
                if not self.region_filter_var.get():
                    self.region_filter_var.set('All')
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load regions: {str(e)}")
            self.regions = []

    def _load_customers(self):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                query = '''
                    SELECT 
                        c.id, c.user_name, c.name, c.phone, p.name as package_name,
                        c.region, CASE WHEN c.status = 1 THEN 'Active' ELSE 'Inactive' END as status
                    FROM customers c
                    LEFT JOIN packages p ON c.package_id = p.id
                    ORDER BY c.name
                '''
                c.execute(query)
                customers = c.fetchall()
                for item in self.tree.get_children():
                    self.tree.delete(item)
                for customer in customers:
                    if customer[6] == 'Inactive':
                        self.tree.insert('', 'end', values=(
                            customer[0], customer[1], customer[2], customer[3] or "",
                            customer[4] or "", customer[5] or "", customer[6]
                        ), tags=("inactive",))
                    else:
                        self.tree.insert('', 'end', values=(
                            customer[0], customer[1], customer[2], customer[3] or "",
                            customer[4] or "", customer[5] or "", customer[6]
                        ))
                self.tree.tag_configure("inactive", background="#FDF6BE")
        except Exception as e:
            logging.error(f"Error loading customers: {str(e)}")
            messagebox.showerror("Error", f"Failed to load customers: {str(e)}")

    def _load_packages(self):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()

                c.execute("SELECT id, name, price FROM packages")
                self.packages = {name: (id, price) for id, name, price in c.fetchall()}
                
                c.execute("SELECT user_name FROM customers")
                self.customer_names = sorted([row[0] for row in c.fetchall()])
                self.customer_combobox['values'] = self.customer_names
                self.customers = {name: name for name in self.customer_names}
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load packages: {str(e)}")
            self.packages = {}
            self.customer_names = []
            self.customers = {}

    def _on_search_change(self, *args):
        search_term = self.customer_search_var.get().lower()
        if not hasattr(self, 'customer_names'):
            return

        if not search_term:
            self.customer_combobox['values'] = self.customer_names
            self._load_customers()
        else:
            filtered = [name for name in self.customer_names if name.lower().startswith(search_term)]
            self.customer_combobox['values'] = filtered

    def _on_customer_selected(self):
        self._load_customers()

    def _format_user_name(self, user_name_var, entry_widget):
        current_text = user_name_var.get()
        cursor_pos = entry_widget.index(tk.INSERT)

        cleaned_text = re.sub(r'[^a-zA-Z0-9.]', '', current_text)

        if len(cleaned_text) > 20:
            cleaned_text = cleaned_text[:20]
            messagebox.showwarning("Input Limit", "User name cannot exceed 20 characters.")

        user_name_var.set(cleaned_text)
        new_cursor_pos = min(cursor_pos, len(cleaned_text))
        entry_widget.icursor(new_cursor_pos)

    def _format_name(self, name_var, entry_widget):
        current_text = name_var.get()
        cursor_pos = entry_widget.index(tk.INSERT)

        cleaned_text = re.sub(r'[^a-zA-Z\s]', '', current_text)
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)

        parts = cleaned_text.split()
        if len(parts) > 3:
            cleaned_text = ' '.join(parts[:3])

        if cleaned_text:
            words = cleaned_text.split()
            formatted = ' '.join(word.capitalize() for word in words)
        else:
            formatted = ''

        if len(formatted) > 20:
            formatted = formatted[:20]
            if ' ' in formatted:
                formatted = formatted.rsplit(' ', 1)[0]
            messagebox.showwarning("Input Limit", "Name cannot exceed 20 characters.")

        name_var.set(formatted)
        new_cursor_pos = min(cursor_pos, len(formatted))
        entry_widget.icursor(new_cursor_pos)

    def _validate_full_name(self, name):
        if not name:
            raise ValueError("Customer name is required")
        if len(name) > 20:
            raise ValueError("Customer name must not exceed 20 characters")
        if not re.match(r'^[a-zA-Z\s]+$', name):
            raise ValueError("Name must contain only letters and spaces")
        if " " not in name.strip():
            raise ValueError("Please enter a full name with a space, e.g., 'Imran Ameen'")
        parts = name.split()
        if len(parts) < 2 or not all(parts):
            raise ValueError("Both first name and last name are required, e.g., 'Imran Ameen'")
        return name

    def _buy_product(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to buy products")
            return
        customer_id = self.tree.item(selected[0])['values'][0]
        customer_name = self.tree.item(selected[0])['values'][2]
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id, name, price FROM products")
                products = c.fetchall()
                c.execute("SELECT product_id, quantity, sold FROM stock")
                stock_data = {row[0]: {'quantity': row[1], 'sold': row[2]} for row in c.fetchall()}
                if not products:
                    messagebox.showerror("Error", "No products available to buy.")
                    return
                # Get customer's credit from billing records
                c.execute('''SELECT COALESCE(SUM(credit_amount), 0) 
                             FROM billing 
                             WHERE customer_id = ? AND status = 'Paid' ''', (customer_id,))
                credit_balance = c.fetchone()[0] or 0.0
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load products: {str(e)}")
            return
        dialog = self._create_dialog()
        dialog.title(f"Buy Product for {customer_name}")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("560x600")
        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], highlightbackground=self.COLORS['border'],
                             highlightthickness=1, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)
        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        tk.Label(main_frame, text=f"Customer: {customer_name}", **label_style).pack(anchor="w", pady=(0, 20))
        tk.Label(main_frame, text=f"Available Credit: {credit_balance:.2f} PKR", **label_style).pack(anchor="w", pady=(0, 10))
        tk.Label(main_frame, text="Select Products:", **label_style).pack(anchor="w", pady=(0, 10))
        product_vars = {}
        for product_id, name, price in products:
            var = tk.IntVar()
            product_vars[(product_id, name, price)] = var
            remaining = 0
            if product_id in stock_data:
                remaining = stock_data[product_id]['quantity'] - stock_data[product_id]['sold']
            frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
            frame.pack(anchor="w", pady=5)
            tk.Checkbutton(frame, text=f"{name} ({price:.2f} PKR)", variable=var,
                          bg=self.COLORS['card_bg'], fg=self.COLORS['text_primary'], selectcolor=self.COLORS['input_bg']).pack(side="left")
            tk.Label(frame, text=f"Available: {remaining}", **label_style).pack(side="left", padx=10)
        total_label = tk.Label(main_frame, text="Total: 0.00 PKR", **label_style)
        total_label.pack(pady=(20, 10))
        after_credit_label = tk.Label(main_frame, text="Amount After Credit: 0.00 PKR", **label_style)
        after_credit_label.pack(pady=(0, 10))
        def update_total():
            total = sum(price for (pid, name, price), var in product_vars.items() if var.get() == 1)
            total_label.config(text=f"Total: {total:.2f} PKR")
            amount_after_credit = max(0, total - credit_balance)
            after_credit_label.config(text=f"Amount After Credit: {amount_after_credit:.2f} PKR")
        for (pid, name, price), var in product_vars.items():
            var.trace_add("write", lambda *args: update_total())
        def submit():
            selected_products = [(pid, name, price) for (pid, name, price), var in product_vars.items()
                               if var.get() == 1]
            if not selected_products:
                messagebox.showerror("Error", "Please select at least one product to buy")
                return
            try:
                # Use direct database operations with connection context manager
                product_ids = [pid for pid, name, price in selected_products]
                total_product_amount = sum(price for pid, name, price in selected_products)
                purchase_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                with get_db_connection() as conn:
                    try:
                        conn.execute("BEGIN")
                        cursor = conn.cursor()

                        # Check if bill already exists for this customer, month, and year
                        cursor.execute('''
                            SELECT id FROM billing
                            WHERE customer_id = ? AND month = ? AND year = ?
                        ''', (customer_id, self.current_month, self.current_year))
                        existing_bill = cursor.fetchone()

                        if existing_bill:
                            raise ValueError(f"A bill already exists for {customer_name} for {self.current_month:02d}/{self.current_year}")

                        # Generate invoice number
                        cursor.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                        max_inv_num = cursor.fetchone()[0] or 0
                        invoice_number = f"INV-{str(max_inv_num + 1).zfill(4)}"

                        # Update product bill calculation to use new signature
                        month_bill_amount = billing_manager._calculate_month_bill_amount(cursor, customer_id, temp_bill_id, total_product_amount, bill_type='product')

                        # Get customer's available credit to determine payment status
                        cursor.execute('SELECT credit_balance FROM customers WHERE id = ?', (customer_id,))
                        row = cursor.fetchone()
                        available_credit = row[0] if row and row[0] else 0.0

                        # Apply credit to the new bill
                        if available_credit >= month_bill_amount:
                            paid_amount = month_bill_amount
                            outstanding_amount = 0.0
                            new_credit_balance = available_credit - month_bill_amount
                            status = 'Paid'
                        else:
                            paid_amount = available_credit
                            outstanding_amount = month_bill_amount - available_credit
                            new_credit_balance = 0.0
                            status = 'Unpaid'

                        # Create the product purchase bill
                        cursor.execute('''
                            INSERT INTO billing (
                                customer_id, month, year, amount, month_bill_amount, paid_amount, outstanding_amount,
                                credit_amount, status, invoice_number, is_manual, created_date
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?, 1, datetime('now'))
                        ''', (customer_id, self.current_month, self.current_year, total_product_amount, month_bill_amount, paid_amount, outstanding_amount, status, invoice_number))

                        bill_id = cursor.lastrowid

                        # Record customer purchases
                        for product_id, name, price in selected_products:
                            cursor.execute('''
                                INSERT INTO customer_purchases (customer_id, product_id, billing_id, purchase_date, price)
                                VALUES (?, ?, ?, ?, ?)
                            ''', (customer_id, product_id, bill_id, purchase_date, price))

                            # Update stock
                            cursor.execute('''UPDATE stock SET sold = sold + 1 WHERE product_id = ?''', (product_id,))

                        # Update customer credit balance and clear previous outstanding/credit since it's now included in this bill
                        cursor.execute('UPDATE customers SET credit_balance = ?, outstanding_amount = 0 WHERE id = ?', (new_credit_balance, customer_id))

                        # Sync customer financials with current bill amounts
                        cursor.execute('''
                            SELECT COALESCE(SUM(outstanding_amount), 0)
                            FROM billing
                            WHERE customer_id = ?
                        ''', (customer_id,))
                        total_outstanding = cursor.fetchone()[0] or 0.0

                        cursor.execute('''
                            UPDATE customers SET outstanding_amount = ?
                            WHERE id = ?
                        ''', (total_outstanding, customer_id))

                        conn.commit()

                        messagebox.showinfo("Success", f"Products purchased successfully for {customer_name}.")
                        dialog.destroy()
                        self._load_customers()
                        self.nav['refresh_billing']()
                        if 'refresh_stock' in self.nav:
                            self.nav['refresh_stock']()

                    except Exception as e:
                        conn.rollback()
                        raise e

            except Exception as e:
                messagebox.showerror("Error", f"Failed to process purchase: {str(e)}")
        button_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        button_frame.pack(pady=20)
        tk.Button(button_frame, text="Buy", command=submit, bg=self.COLORS['button_start'],
                  fg="#FFFFFF", font=("Helvetica", 10, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['button_end']).pack()

    def _generate_invoice_number(self):
        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing WHERE invoice_number IS NOT NULL")
                max_inv_num = c.fetchone()[0] or 0
                return f"INV-{str(max_inv_num + 1).zfill(4)}"
        except Exception as e:
            logging.error(f"Error generating invoice number: {str(e)}")
            return f"INV-{str(1).zfill(4)}"

    def _suspend_customer(self):
        selected = self.tree.selection()
        if not selected:
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_name = self.tree.item(selected[0])['values'][2]

        if messagebox.askyesno("Confirm", f"Suspend account for {customer_name}?"):
            try:
                with get_db_connection() as conn:
                    c = conn.cursor()
                    c.execute("UPDATE customers SET status = 0 WHERE id = ?", (customer_id,))
                    messagebox.showinfo("Success", f"{customer_name} has been suspended.")
                    self.tree.item(selected[0], tags=('suspended',))
                    self._load_customers()
                    self._on_row_select(None)
            except Exception as e:
                messagebox.showerror("Error", f"Failed to suspend customer: {str(e)}")

    def _activate_customer(self):
        selected = self.tree.selection()
        if not selected:
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_name = self.tree.item(selected[0])['values'][2]
        current_package = self.tree.item(selected[0])['values'][4]

        dialog = self._create_dialog()
        dialog.title("Activate Customer")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("550x350")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], 
                             highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        tk.Label(main_frame, text="Activate Customer", bg=self.COLORS['card_bg'], 
                 font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        dropdown_style = {"font": ("Helvetica", 12), "width": 30}

        package_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        package_frame.pack(fill="x", pady=(0, 15))
        tk.Label(package_frame, text="Select Package:", **label_style, width=15, anchor="w").pack(side="left")
        package_var = tk.StringVar(value=current_package)
        package_dropdown = ttk.Combobox(package_frame, textvariable=package_var, values=list(self.packages.keys()),
                                       state="readonly", **dropdown_style)
        package_dropdown.pack(side="left")

        def submit():
            try:
                package_name = package_var.get()
                if not package_name:
                    raise ValueError("Please select a package")

                with get_db_connection() as conn:
                    c = conn.cursor()

                    package_id = self.packages[package_name][0]
                    c.execute("UPDATE customers SET status = 1, package_id = ? WHERE id = ?", (package_id, customer_id))

                    # Create new bill for current month
                    package_price = self.packages[package_name][1]
                    c.execute('''INSERT OR IGNORE INTO billing (customer_id, month, year, amount, is_paid)
                                 VALUES (?, ?, ?, ?, ?)''',
                              (customer_id, self.current_month, self.current_year, package_price, 0))

                    c.execute("UPDATE customers SET package_change_date = ? WHERE id = ?",
                              (datetime.now().strftime('%Y-%m-%d'), customer_id))

                    conn.commit()

                messagebox.showinfo("Success", f"{customer_name} has been activated with package {package_name}.")
                dialog.destroy()
                self._load_customers()
                self.nav['refresh_billing']()
            except ValueError as ve:
                messagebox.showerror("Error", str(ve))
            except Exception as e:
                messagebox.showerror("Error", f"Failed to activate customer: {str(e)}")

        tk.Button(main_frame, text="Activate", command=submit, bg=self.COLORS['primary_accent'],
                  fg="#FFFFFF", font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center")

    def _change_package_plan(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to change package plan")
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_name = self.tree.item(selected[0])['values'][2]
        current_package = self.tree.item(selected[0])['values'][4]

        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT package_change_date FROM customers WHERE id = ?", (customer_id,))
                change_date = c.fetchone()[0]
                if change_date:
                    change_dt = datetime.strptime(change_date, '%Y-%m-%d')
                    if change_dt.month == self.current_month and change_dt.year == self.current_year:
                        messagebox.showerror("Error", "Package can only be changed once per month. Wait until next month.")
                        return
        except Exception as e:
            messagebox.showerror("Error", f"Failed to check package change date: {str(e)}")
            return

        dialog = self._create_dialog()
        dialog.title("Change Package Plan")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("550x350")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], 
                             highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        tk.Label(main_frame, text="Change Package Plan", bg=self.COLORS['card_bg'], 
                 font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        dropdown_style = {"font": ("Helvetica", 12), "width": 30}

        package_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        package_frame.pack(fill="x", pady=(0, 15))
        tk.Label(package_frame, text="Select New Package:", **label_style, width=15, anchor="w").pack(side="left")
        package_var = tk.StringVar()
        package_dropdown = ttk.Combobox(package_frame, textvariable=package_var, values=list(self.packages.keys()),
                                       state="readonly", **dropdown_style)
        package_dropdown.pack(side="left")

        def submit():
            try:
                package_name = package_var.get()
                if not package_name:
                    raise ValueError("Please select a package")
                if package_name == current_package:
                    raise ValueError("Please select a different package")

                with get_db_connection() as conn:
                    c = conn.cursor()

                    package_id, package_price = self.packages[package_name]
                    c.execute("UPDATE customers SET package_id = ?, package_change_date = ? WHERE id = ?",
                              (package_id, datetime.now().strftime('%Y-%m-%d'), customer_id))

                    c.execute('''INSERT OR IGNORE INTO billing (customer_id, month, year, amount, is_paid)
                                 VALUES (?, ?, ?, ?, ?)''',
                              (customer_id, self.current_month, self.current_year, package_price, 0))

                    conn.commit()

                messagebox.showinfo("Success", f"Package plan for {customer_name} changed to {package_name}.")
                dialog.destroy()
                self._load_customers()
                self.nav['refresh_billing']()
            except ValueError as ve:
                messagebox.showerror("Error", str(ve))
            except Exception as e:
                messagebox.showerror("Error", f"Failed to change package plan: {str(e)}")

        tk.Button(main_frame, text="Change Plan", command=submit, bg=self.COLORS['primary_accent'],
                  fg="#FFFFFF", font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center")

    def _add_customer(self):
        dialog = self._create_dialog()
        dialog.title("Add Customer")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("550x400")

        # Center the dialog on the screen
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], 
                             highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        tk.Label(main_frame, text="Add Customer", bg=self.COLORS['card_bg'], 
                 font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        entry_style = {"font": ("Helvetica", 12)}
        dropdown_style = {"font": ("Helvetica", 12), "width": 30}

        user_name_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        user_name_frame.pack(fill="x", pady=(0, 15))
        tk.Label(user_name_frame, text="User Name:", **label_style, width=15, anchor="w").pack(side="left")
        user_name_var = tk.StringVar()
        user_name_entry = tk.Entry(user_name_frame, textvariable=user_name_var, **entry_style, width=30)
        user_name_entry.pack(side="left")
        user_name_entry.bind("<KeyRelease>", lambda e: self._format_user_name(user_name_var, user_name_entry))

        name_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        name_frame.pack(fill="x", pady=(0, 15))
        tk.Label(name_frame, text="Customer Name:", **label_style, width=15, anchor="w").pack(side="left")
        name_var = tk.StringVar()
        name_entry = tk.Entry(name_frame, textvariable=name_var, **entry_style, width=30)
        name_entry.pack(side="left")
        name_entry.bind("<KeyRelease>", lambda e: self._format_name(name_var, name_entry))

        phone_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        phone_frame.pack(fill="x", pady=(0, 15))
        tk.Label(phone_frame, text="Phone Number:", **label_style, width=15, anchor="w").pack(side="left")
        phone_var = tk.StringVar()
        phone_entry = tk.Entry(phone_frame, textvariable=phone_var, **entry_style, width=30)
        phone_entry.pack(side="left")

        package_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        package_frame.pack(fill="x", pady=(0, 15))
        tk.Label(package_frame, text="Select Package:", **label_style, width=15, anchor="w").pack(side="left")
        package_var = tk.StringVar()
        package_dropdown = ttk.Combobox(package_frame, textvariable=package_var, values=list(self.packages.keys()),
                                       state="readonly", **dropdown_style)
        package_dropdown.pack(side="left")

        region_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        region_frame.pack(fill="x", pady=(0, 30))
        tk.Label(region_frame, text="Select Region:", **label_style, width=15, anchor="w").pack(side="left")
        region_var = tk.StringVar()
        region_dropdown = ttk.Combobox(region_frame, textvariable=region_var, values=self.regions,
                                      state="readonly", **dropdown_style)
        region_dropdown.pack(side="left")

        def submit():
            try:
                user_name = self._validate_user_name(user_name_var.get().strip())
                name = self._validate_full_name(name_var.get().strip())
                phone = phone_var.get().strip() or None
                package_name = package_var.get()
                region = region_var.get()

                if phone and not self._validate_phone(phone):
                    raise ValueError("Phone number must be 11 digits starting with '03', e.g., 03123456789, or left empty")
                if not package_name:
                    raise ValueError("Please select a package")
                if not region:
                    raise ValueError("Please select a region")

                with get_db_connection() as conn:
                    c = conn.cursor()

                    c.execute("SELECT id FROM customers WHERE user_name = ? OR (phone = ? AND phone IS NOT NULL)", (user_name, phone))
                    if c.fetchone():
                        raise ValueError("Customer with this user name or phone number already exists")

                    package_id = self.packages[package_name][0]
                    create_date = datetime.now().strftime('%Y-%m-%d')

                    c.execute('''INSERT INTO customers (user_name, name, phone, package_id, create_date, status, region)
                                 VALUES (?, ?, ?, ?, ?, ?, ?)''',
                              (user_name, name, phone, package_id, create_date, 1, region))
                    c.execute("SELECT last_insert_rowid()")
                    customer_id = c.fetchone()[0]

                    package_price = self.packages[package_name][1]
                    c.execute('''INSERT INTO billing (customer_id, month, year, amount, is_paid)
                                 VALUES (?, ?, ?, ?, ?)''',
                              (customer_id, self.current_month, self.current_year, package_price, 0))

                messagebox.showinfo("Success", f"Customer {name} added successfully with package {package_name}.")
                dialog.destroy()
                
                self._load_customers()
                self._load_packages()
                
                for item in self.tree.get_children():
                    if self.tree.item(item)['values'][1] == user_name:
                        self.tree.selection_set(item)
                        self.tree.see(item)
                        break
        
            except ValueError as ve:
                messagebox.showerror("Error", str(ve))
            except Exception as e:
                messagebox.showerror("Error", f"Failed to add customer: {str(e)}")

        tk.Button(main_frame, text="Add Customer", command=submit, bg=self.COLORS['primary_accent'],
                  fg="#FFFFFF", font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center")

    def _edit_customer(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to edit")
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_user_name = self.tree.item(selected[0])['values'][1]
        customer_name = self.tree.item(selected[0])['values'][2]
        customer_phone = self.tree.item(selected[0])['values'][3] or ""
        customer_package = self.tree.item(selected[0])['values'][4] or ""
        customer_region = self.tree.item(selected[0])['values'][5]

        dialog = self._create_dialog()
        dialog.title("Edit Customer")
        dialog.configure(bg=self.COLORS['background'])
        dialog.geometry("550x400")

        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], 
                             highlightbackground=self.COLORS['border'], 
                             highlightthickness=2, padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        tk.Label(main_frame, text="Edit Customer", bg=self.COLORS['card_bg'], 
                 font=("Helvetica", 16, "bold"), fg=self.COLORS['primary_accent']).pack(anchor="center", pady=(0, 20))

        label_style = {"bg": self.COLORS['card_bg'], "font": ("Helvetica", 10), "fg": self.COLORS['text_primary']}
        entry_style = {"font": ("Helvetica", 12)}
        dropdown_style = {"font": ("Helvetica", 12), "width": 30}

        user_name_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        user_name_frame.pack(fill="x", pady=(0, 15))
        tk.Label(user_name_frame, text="User Name:", **label_style, width=15, anchor="w").pack(side="left")
        user_name_var = tk.StringVar(value=customer_user_name)
        user_name_entry = tk.Entry(user_name_frame, textvariable=user_name_var, **entry_style, width=30)
        user_name_entry.pack(side="left")
        user_name_entry.bind("<KeyRelease>", lambda e: self._format_user_name(user_name_var, user_name_entry))

        name_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        name_frame.pack(fill="x", pady=(0, 15))
        tk.Label(name_frame, text="Customer Name:", **label_style, width=15, anchor="w").pack(side="left")
        name_var = tk.StringVar(value=customer_name)
        name_entry = tk.Entry(name_frame, textvariable=name_var, **entry_style, width=30)
        name_entry.pack(side="left")
        name_entry.bind("<KeyRelease>", lambda e: self._format_name(name_var, name_entry))

        phone_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        phone_frame.pack(fill="x", pady=(0, 15))
        tk.Label(phone_frame, text="Phone Number:", **label_style, width=15, anchor="w").pack(side="left")
        phone_var = tk.StringVar(value=customer_phone)
        phone_entry = tk.Entry(phone_frame, textvariable=phone_var, **entry_style, width=30)
        phone_entry.pack(side="left")

        package_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        package_frame.pack(fill="x", pady=(0, 15))
        tk.Label(package_frame, text="Select Package:", **label_style, width=15, anchor="w").pack(side="left")
        package_var = tk.StringVar(value=customer_package)
        package_dropdown = ttk.Combobox(package_frame, textvariable=package_var, values=list(self.packages.keys()),
                                       **dropdown_style)
        package_dropdown.configure(state="readonly" if customer_package in ["", "None", None] else "disabled")
        package_dropdown.pack(side="left")

        region_frame = tk.Frame(main_frame, bg=self.COLORS['card_bg'])
        region_frame.pack(fill="x", pady=(0, 30))
        tk.Label(region_frame, text="Select Region:", **label_style, width=15, anchor="w").pack(side="left")
        region_var = tk.StringVar(value=customer_region)
        region_dropdown = ttk.Combobox(region_frame, textvariable=region_var, values=self.regions,
                                      state="readonly", **dropdown_style)
        region_dropdown.pack(side="left")

        def submit():
            try:
                user_name = self._validate_user_name(user_name_var.get().strip())
                name = self._validate_full_name(name_var.get().strip())
                phone = phone_var.get().strip() or None
                package_name = package_var.get() if customer_package in ["", "None", None] else customer_package
                region = region_var.get()

                if phone and not self._validate_phone(phone):
                    raise ValueError("Phone number must be 11 digits starting with '03', e.g., 03123456789, or left empty")
                if not package_name:
                    raise ValueError("Please select a package")
                if not region:
                    raise ValueError("Please select a region")

                with get_db_connection() as conn:
                    c = conn.cursor()

                    c.execute("SELECT id FROM customers WHERE (user_name = ? OR (phone = ? AND phone IS NOT NULL)) AND id != ?", (user_name, phone, customer_id))
                    if c.fetchone():
                        raise ValueError("Customer with this user name or phone number already exists")

                    package_id = self.packages[package_name][0]
                    c.execute('''UPDATE customers 
                                 SET user_name = ?, name = ?, phone = ?, package_id = ?, region = ?
                                 WHERE id = ?''',
                              (user_name, name, phone, package_id, region, customer_id))

                    if customer_package in ["", "None", None] and package_name:
                        package_price = self.packages[package_name][1]
                        c.execute("UPDATE customers SET package_change_date = ? WHERE id = ?",
                                  (datetime.now().strftime('%Y-%m-%d'), customer_id))
                        c.execute('''INSERT OR IGNORE INTO billing (customer_id, month, year, amount, is_paid)
                                     VALUES (?, ?, ?, ?, ?)''',
                                  (customer_id, self.current_month, self.current_year, package_price, 0))

                    conn.commit()

                messagebox.showinfo("Success", f"Customer {name} updated successfully." + 
                                    (f" New bill of {self.packages[package_name][1]:.2f} PKR added for {self.current_month:02d}/{self.current_year}." if customer_package in ["", "None", None] and package_name else ""))
                dialog.destroy()
                self._load_customers()
                self.nav['refresh_billing']()
            except ValueError as ve:
                messagebox.showerror("Error", str(ve))
            except Exception as e:
                messagebox.showerror("Error", f"Failed to update customer: {str(e)}")

        tk.Button(main_frame, text="Update Customer", command=submit, bg=self.COLORS['primary_accent'],
                  fg="#FFFFFF", font=("Helvetica", 12, "bold"), relief=tk.FLAT, padx=20, pady=10,
                  activebackground=self.COLORS['secondary_accent'], activeforeground="#FFFFFF").pack(anchor="center")

    def _show_billing(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to view billing")
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        self.nav['show_billing'](customer_id)

    def refresh_customers(self):
        self._load_customers()

    def _create_dialog(self, *args, **kwargs):
        dialog = tk.Toplevel(self)
        dialog.transient(self.winfo_toplevel())
        dialog.grab_set()
        dialog.focus_set()
        dialog.protocol("WM_DELETE_WINDOW", lambda d=dialog: self._close_dialog(d))
        self._dialogs.append(dialog)
        return dialog

    def _close_dialog(self, dialog):
        if dialog in self._dialogs:
            self._dialogs.remove(dialog)
        dialog.destroy()

    def _on_main_minimize(self, event=None):
        for dialog in self._dialogs:
            try:
                dialog.iconify()
            except Exception:
                pass

    def _on_main_restore(self, event=None):
        for dialog in self._dialogs:
            try:
                dialog.deiconify()
                dialog.lift()
            except Exception:
                pass

    def _delete_customer(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to delete")
            return

        customer_id = self.tree.item(selected[0])['values'][0]
        customer_name = self.tree.item(selected[0])['values'][2]

        if not messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete customer '{customer_name}' and all related records? This cannot be undone."):
            return

        try:
            with get_db_connection() as conn:
                c = conn.cursor()
                # Delete purchases linked to this customer's bills
                c.execute("DELETE FROM customer_purchases WHERE billing_id IN (SELECT id FROM billing WHERE customer_id = ?)", (customer_id,))
                # Delete purchases directly linked to this customer (if any remain)
                c.execute("DELETE FROM customer_purchases WHERE customer_id = ?", (customer_id,))
                # Delete payment history
                c.execute("DELETE FROM payment_history WHERE customer_id = ?", (customer_id,))
                # Delete billing
                c.execute("DELETE FROM billing WHERE customer_id = ?", (customer_id,))
                # Delete customer
                c.execute("DELETE FROM customers WHERE id = ?", (customer_id,))
                conn.commit()
            messagebox.showinfo("Success", f"Customer '{customer_name}' and all related records have been deleted.")
            self._load_customers()
            if 'refresh_billing' in self.nav:
                self.nav['refresh_billing']()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to delete customer: {str(e)}")

    def _select_customer_by_id(self):
        """Select and focus the customer row by customer_id if provided."""
        for item in self.tree.get_children():
            if str(self.tree.item(item)['values'][0]) == str(self.customer_id):
                self.tree.selection_set(item)
                self.tree.see(item)
                break
