#!/usr/bin/env python3
"""
Test script to verify that credit and outstanding amounts are correctly stored in 
payment_history table and displayed from there without overriding between bills.
"""

import sqlite3
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_payment_history_display():
    """
    Test the payment history display scenario:
    1. Customer has July bill (ID 0143) - paid
    2. Create June manual bill (ID 0144) with 2500 + 500 outstanding = 3000
    3. Customer pays 3500 for June bill
    4. Verify June bill shows 500 credit in payment_history
    5. Verify July bill shows 0 credit/outstanding
    6. Verify billing page displays correct amounts from payment_history
    """
    print("Testing payment history display logic...")
    
    # Connect to database
    conn = sqlite3.connect('crm_system.db')
    c = conn.cursor()
    
    try:
        # Clean up test data
        c.execute("DELETE FROM payment_history WHERE customer_id IN (SELECT id FROM customers WHERE name = 'Test Payment History')")
        c.execute("DELETE FROM billing WHERE customer_id IN (SELECT id FROM customers WHERE name = 'Test Payment History')")
        c.execute("DELETE FROM customers WHERE name = 'Test Payment History'")
        
        # Create test customer
        c.execute('''
            INSERT INTO customers (name, username, phone, package_id, region, status, 
                                 credit_balance, outstanding_amount)
            VALUES ('Test Payment History', 'testpayment', '1234567890', 1, 'Test', 1, 0, 0)
        ''')
        customer_id = c.lastrowid
        
        print(f"Created test customer with ID: {customer_id}")
        
        # STEP 1: Create July bill (current month) - paid
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual, paid_date, paid_by)
            VALUES (?, 7, 2024, 1000, 1000, 1000, 0, 0, 'Paid', 'INV-0143', 0, 
                    datetime('now'), 'Test User')
        ''', (customer_id,))
        july_bill_id = c.lastrowid
        
        # Record payment history for July bill (no credit/outstanding)
        c.execute('''
            INSERT INTO payment_history
            (bill_id, invoice_number, customer_id, payment_date, amount_paid,
             credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
             payment_method, recorded_by, timestamp)
            VALUES (?, 'INV-0143', ?, datetime('now'), 1000, 0, 0, 0, 0, 'Cash', 'Test User', datetime('now'))
        ''', (july_bill_id, customer_id))
        
        print(f"Created July bill (ID: {july_bill_id}) with payment history")
        
        # STEP 2: Create June manual bill
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual, created_date)
            VALUES (?, 6, 2024, 2500, 3000, 3500, 0, 0, 'Paid', 'INV-0144', 1, datetime('now'))
        ''', (customer_id,))
        june_bill_id = c.lastrowid
        
        print(f"Created June manual bill (ID: {june_bill_id})")
        
        # STEP 3: Record payment history for June bill with 500 credit
        # Payment: 3500, Month Bill: 3000, Credit: 500
        c.execute('''
            INSERT INTO payment_history
            (bill_id, invoice_number, customer_id, payment_date, amount_paid,
             credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
             payment_method, recorded_by, timestamp)
            VALUES (?, 'INV-0144', ?, datetime('now'), 3500, 500, 0, 0, 500, 'Cash', 'Test User', datetime('now'))
        ''', (june_bill_id, customer_id))
        
        print("Recorded payment history for June bill with 500 credit")
        
        # STEP 4: Test the billing display logic
        def get_bill_display_amounts(bill_id):
            """Simulate the billing display logic"""
            c.execute('''
                SELECT 
                    COALESCE(SUM(credit_amount), 0) as total_credit,
                    COALESCE(SUM(outstanding_amount), 0) as total_outstanding
                FROM payment_history
                WHERE bill_id = ?
            ''', (bill_id,))
            history_result = c.fetchone()
            
            if history_result:
                bill_credit = history_result[0] or 0.0
                bill_outstanding = history_result[1] or 0.0
            else:
                bill_credit = 0.0
                bill_outstanding = 0.0
            
            return bill_credit, bill_outstanding
        
        # Test July bill display
        july_credit, july_outstanding = get_bill_display_amounts(july_bill_id)
        print(f"July bill display: Credit={july_credit}, Outstanding={july_outstanding}")
        
        # Test June bill display
        june_credit, june_outstanding = get_bill_display_amounts(june_bill_id)
        print(f"June bill display: Credit={june_credit}, Outstanding={june_outstanding}")
        
        # STEP 5: Verify results
        print("\n=== Verification ===")
        
        # Check payment history records
        c.execute('''
            SELECT bill_id, invoice_number, amount_paid, credit_amount, outstanding_amount
            FROM payment_history
            WHERE customer_id = ?
            ORDER BY bill_id
        ''', (customer_id,))
        payment_records = c.fetchall()
        
        print("Payment history records:")
        for record in payment_records:
            bill_id, invoice, paid, credit, outstanding = record
            print(f"  Bill {bill_id} ({invoice}): Paid={paid}, Credit={credit}, Outstanding={outstanding}")
        
        # Assertions
        assert july_credit == 0, f"July bill should have 0 credit, got {july_credit}"
        assert july_outstanding == 0, f"July bill should have 0 outstanding, got {july_outstanding}"
        assert june_credit == 500, f"June bill should have 500 credit, got {june_credit}"
        assert june_outstanding == 0, f"June bill should have 0 outstanding, got {june_outstanding}"
        
        # Verify payment records
        july_payment = next((r for r in payment_records if r[0] == july_bill_id), None)
        june_payment = next((r for r in payment_records if r[0] == june_bill_id), None)
        
        assert july_payment is not None, "July payment record should exist"
        assert june_payment is not None, "June payment record should exist"
        
        assert july_payment[2] == 1000, f"July payment should be 1000, got {july_payment[2]}"
        assert july_payment[3] == 0, f"July payment credit should be 0, got {july_payment[3]}"
        
        assert june_payment[2] == 3500, f"June payment should be 3500, got {june_payment[2]}"
        assert june_payment[3] == 500, f"June payment credit should be 500, got {june_payment[3]}"
        
        print("\n🎉 All tests passed! Payment history display is working correctly.")
        print("✓ Each bill shows its own credit/outstanding from payment_history")
        print("✓ No overriding of values between bills")
        print("✓ June bill correctly shows 500 credit")
        print("✓ July bill correctly shows 0 credit/outstanding")
        
        # STEP 6: Test multiple payments for same bill
        print("\n=== Testing Multiple Payments for Same Bill ===")
        
        # Create another bill and make multiple payments
        c.execute('''
            INSERT INTO billing (customer_id, month, year, amount, month_bill_amount, 
                               paid_amount, outstanding_amount, credit_amount, status, 
                               invoice_number, is_manual, created_date)
            VALUES (?, 8, 2024, 1000, 1000, 800, 0, 0, 'Partially Paid', 'INV-0145', 0, datetime('now'))
        ''', (customer_id,))
        august_bill_id = c.lastrowid
        
        # First payment: 500 (partial)
        c.execute('''
            INSERT INTO payment_history
            (bill_id, invoice_number, customer_id, payment_date, amount_paid,
             credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
             payment_method, recorded_by, timestamp)
            VALUES (?, 'INV-0145', ?, datetime('now'), 500, 0, 500, 500, 0, 'Cash', 'Test User', datetime('now'))
        ''', (august_bill_id, customer_id))
        
        # Second payment: 300 (partial)
        c.execute('''
            INSERT INTO payment_history
            (bill_id, invoice_number, customer_id, payment_date, amount_paid,
             credit_amount, outstanding_amount, remaining_outstanding, applied_credit,
             payment_method, recorded_by, timestamp)
            VALUES (?, 'INV-0145', ?, datetime('now'), 300, 0, 200, 200, 0, 'Cash', 'Test User', datetime('now'))
        ''', (august_bill_id, customer_id))
        
        # Test display for bill with multiple payments
        august_credit, august_outstanding = get_bill_display_amounts(august_bill_id)
        print(f"August bill display (multiple payments): Credit={august_credit}, Outstanding={august_outstanding}")
        
        # Should show total outstanding from latest payment
        assert august_outstanding == 200, f"August bill should have 200 outstanding, got {august_outstanding}"
        assert august_credit == 0, f"August bill should have 0 credit, got {august_credit}"
        
        print("✓ Multiple payments for same bill handled correctly")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    success = test_payment_history_display()
    sys.exit(0 if success else 1)
